'use client';

import { useState } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';

export default function Layout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  // const [isMobile, setIsMobile] = useState(false);

  // useEffect(() => {
  //   const checkMobile = () => setIsMobile(window.innerWidth <= 768);
  //   checkMobile();
  //   window.addEventListener('resize', checkMobile);
  //   return () => window.removeEventListener('resize', checkMobile);
  // }, []);

  // console.log(isMobile);
  

  return (
    <div className="flex h-screen overflow-hidden">
       <Sidebar
        isOpenFromHeader={isSidebarOpen}
        setIsOpenFromHeader={setIsSidebarOpen}
      />
      <div className="flex flex-col flex-1">
        <div className="sticky top-0 z-10 bg-white">
         <Header onSidebarToggle={() => setIsSidebarOpen((prev) => !prev)} />
        </div>
        <main className="flex-1 overflow-auto p-6 bg-gray-50">{children}</main>
      </div>
    </div>
  );
}
