'use client';

import { Button } from '@heroui/react';
import { useState } from 'react';

import LoginModal from '@/components/dashboard/LoginModal';

export default function Home() {
  const [openLoginModal, setLoginModal] = useState(false);
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <h1 className="text-4xl font-bold text-primary">
          Welcome to NxVoy! <br />
          Meet SHASA, Your Travel Companion!
        </h1>
        <p>
          She’s an AI trip planner and a great itinerary master who makes every
          journey effortless and joyful!
        </p>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <div className="p-[2px] rounded-full bg-gradient-to-r from-blue-400 to-pink-500 shadow-sm hover:shadow-md transition-all">
            <Button
              type="button"
              onPress={() => setLoginModal(!openLoginModal)}
              className="hover:cursor-pointer inline-flex items-center h-10 sm:h-12 px-4 sm:px-5 sm:w-auto bg-white rounded-full font-medium text-sm sm:text-base text-black"
            >
              Chat with Shasa
            </Button>
          </div>
          <Button
            type="button"
            color="primary"
            variant="bordered"
            className="shadow-md text-sm sm:text-base rounded-full h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
          >
            Start Booking
          </Button>
          <LoginModal open={openLoginModal} />
        </div>
      </main>
    </div>
  );
}
