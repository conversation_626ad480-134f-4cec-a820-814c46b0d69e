// app/layout.tsx

import { Inter, Poppins } from 'next/font/google';
import { Toaster } from 'react-hot-toast';
import { QueryProvider } from '@/providers/query';
import '@/styles/globals.css';
import type { ChildrenProps } from '@/types';
import { Providers } from '@/providers/providers';


const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  adjustFontFallback: false,
});

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

export const metadata = {
  title: 'NxVoy',
  description: ''
};

function RootLayoutContent({ children }: ChildrenProps) {
  return (
    
      <html lang="en" suppressHydrationWarning className="overflow-x-hidden">
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased overflow-x-hidden`}
      >
      <Providers>
        <QueryProvider>
          <div className="flex min-h-screen bg-[var(--background)] w-full overflow-x-hidden">
            <div className="flex-1 flex flex-col w-full">
              <main className="flex-1 w-full overflow-x-hidden">
                {children}
              </main>
            </div>
          </div>
          <Toaster
            position="bottom-right"
            toastOptions={{
              className:
                'bg-[var(--card)] text-[var(--foreground)] border-[var(--border)]',
              duration: 3000,
            }}
          />
        </QueryProvider>
          </Providers>
        </body>
      </html>
  );
}

export default function RootLayout({ children }: ChildrenProps) {
  return (
    <html lang="en" suppressHydrationWarning className="overflow-x-hidden">
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased overflow-x-hidden`}
      >
        <RootLayoutContent>{children}</RootLayoutContent>
      </body>
    </html>
  );
}
